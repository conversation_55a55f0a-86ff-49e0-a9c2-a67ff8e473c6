import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  ResponsiveGrid,
  Progress,
  Badge,
  Modal,
  Form,
  FormItem,
  Input,
  Select,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';
import { useRecentFacebookAdsCampaigns } from '../../hooks/facebook-ads/useFacebookAdsCampaigns';

interface BudgetItem {
  id: string;
  name: string;
  type: 'account' | 'campaign';
  totalBudget: number;
  spentBudget: number;
  remainingBudget: number;
  budgetType: 'daily' | 'lifetime';
  status: 'active' | 'paused' | 'exhausted' | 'warning';
  currency: string;
  startDate: string;
  endDate?: string;
  spendRate: number; // VND per day
  estimatedDaysLeft?: number;
}

interface BudgetFormData {
  budgetType: 'daily' | 'lifetime';
  amount: number;
  startDate: string;
  endDate?: string;
}

interface FacebookBudgetManagerProps {
  /**
   * Hiển thị loading state
   */
  isLoading?: boolean;
  
  /**
   * Callback khi cập nhật budget
   */
  onBudgetUpdate?: (itemId: string, newBudget: BudgetFormData) => void;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Facebook Budget Manager Component
 * Quản lý ngân sách cho tài khoản và chiến dịch
 */
const FacebookBudgetManager: React.FC<FacebookBudgetManagerProps> = ({
  isLoading = false,
  onBudgetUpdate,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [showBudgetModal, setShowBudgetModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<BudgetItem | null>(null);
  const [budgetFilter, setBudgetFilter] = useState<'all' | 'warning' | 'exhausted'>('all');

  const { formRef, setFormErrors } = useFormErrors<BudgetFormData>();
  const [formData, setFormData] = useState<BudgetFormData>({
    budgetType: 'daily',
    amount: 0,
    startDate: new Date().toISOString().split('T')[0],
  });

  const {
    adAccounts,
    isLoading: authLoading,
  } = useFacebookAuth();

  const {
    data: campaignsData,
    isLoading: campaignsLoading,
  } = useRecentFacebookAdsCampaigns(20);

  // Mock budget data - In real app, this would come from API
  const budgetItems: BudgetItem[] = useMemo(() => {
    const items: BudgetItem[] = [];
    
    // Add account budgets
    adAccounts.forEach((account) => {
      const totalBudget = Math.floor(Math.random() * ********) + 1000000;
      const spentBudget = Math.floor(totalBudget * (0.3 + Math.random() * 0.6));
      const spendRate = Math.floor(Math.random() * 200000) + 50000;
      const remainingBudget = totalBudget - spentBudget;
      const estimatedDaysLeft = Math.floor(remainingBudget / spendRate);
      
      let status: BudgetItem['status'] = 'active';
      if (remainingBudget <= 0) status = 'exhausted';
      else if (remainingBudget < totalBudget * 0.2) status = 'warning';
      
      items.push({
        id: `account_${account.id}`,
        name: account.name,
        type: 'account',
        totalBudget,
        spentBudget,
        remainingBudget,
        budgetType: 'daily',
        status,
        currency: account.currency || 'VND',
        startDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        spendRate,
        estimatedDaysLeft: estimatedDaysLeft > 0 ? estimatedDaysLeft : undefined,
      });
    });

    // Add campaign budgets
    const campaigns = campaignsData?.result?.items || [];
    campaigns.slice(0, 10).forEach((campaign) => {
      const totalBudget = Math.floor(Math.random() * 5000000) + 500000;
      const spentBudget = Math.floor(totalBudget * (0.2 + Math.random() * 0.7));
      const spendRate = Math.floor(Math.random() * 100000) + 20000;
      const remainingBudget = totalBudget - spentBudget;
      const estimatedDaysLeft = Math.floor(remainingBudget / spendRate);
      
      let status: BudgetItem['status'] = 'active';
      if (remainingBudget <= 0) status = 'exhausted';
      else if (remainingBudget < totalBudget * 0.2) status = 'warning';
      
      items.push({
        id: `campaign_${campaign.id}`,
        name: campaign.name,
        type: 'campaign',
        totalBudget,
        spentBudget,
        remainingBudget,
        budgetType: Math.random() > 0.5 ? 'daily' : 'lifetime',
        status,
        currency: 'VND',
        startDate: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString(),
        spendRate,
        estimatedDaysLeft: estimatedDaysLeft > 0 ? estimatedDaysLeft : undefined,
      });
    });

    return items;
  }, [adAccounts, campaignsData]);

  // Filter budget items
  const filteredBudgetItems = useMemo(() => {
    if (budgetFilter === 'all') return budgetItems;
    return budgetItems.filter(item => item.status === budgetFilter);
  }, [budgetItems, budgetFilter]);

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    const totalBudget = budgetItems.reduce((sum, item) => sum + item.totalBudget, 0);
    const totalSpent = budgetItems.reduce((sum, item) => sum + item.spentBudget, 0);
    const totalRemaining = budgetItems.reduce((sum, item) => sum + item.remainingBudget, 0);
    const warningCount = budgetItems.filter(item => item.status === 'warning').length;
    const exhaustedCount = budgetItems.filter(item => item.status === 'exhausted').length;

    return {
      totalBudget,
      totalSpent,
      totalRemaining,
      spentPercentage: (totalSpent / totalBudget) * 100,
      warningCount,
      exhaustedCount,
    };
  }, [budgetItems]);

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'VND') => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  // Get status config
  const getStatusConfig = (status: BudgetItem['status']) => {
    switch (status) {
      case 'active':
        return { variant: 'success' as const, label: t('common:status.active', 'Hoạt động'), color: 'text-green-600' };
      case 'warning':
        return { variant: 'warning' as const, label: t('marketing:facebookAds.budget.warning', 'Cảnh báo'), color: 'text-yellow-600' };
      case 'exhausted':
        return { variant: 'destructive' as const, label: t('marketing:facebookAds.budget.exhausted', 'Hết ngân sách'), color: 'text-red-600' };
      case 'paused':
        return { variant: 'secondary' as const, label: t('common:status.paused', 'Tạm dừng'), color: 'text-gray-600' };
      default:
        return { variant: 'secondary' as const, label: status, color: 'text-gray-600' };
    }
  };

  // Handle edit budget
  const handleEditBudget = (item: BudgetItem) => {
    setSelectedItem(item);
    setFormData({
      budgetType: item.budgetType,
      amount: item.totalBudget,
      startDate: item.startDate.split('T')[0],
      endDate: item.endDate?.split('T')[0],
    });
    setShowBudgetModal(true);
  };

  // Handle form submit
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    const errors: Partial<BudgetFormData> = {};
    if (formData.amount <= 0) {
      errors.amount = t('marketing:facebookAds.budget.errors.invalidAmount', 'Số tiền phải lớn hơn 0');
    }
    if (!formData.startDate) {
      errors.startDate = t('marketing:facebookAds.budget.errors.startDateRequired', 'Ngày bắt đầu là bắt buộc');
    }
    if (formData.budgetType === 'lifetime' && !formData.endDate) {
      errors.endDate = t('marketing:facebookAds.budget.errors.endDateRequired', 'Ngày kết thúc là bắt buộc cho ngân sách trọn đời');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    if (selectedItem) {
      onBudgetUpdate?.(selectedItem.id, formData);
    }
    
    setShowBudgetModal(false);
    setSelectedItem(null);
  };

  // Filter options
  const filterOptions = [
    { value: 'all', label: t('marketing:facebookAds.budget.filters.all', 'Tất cả') },
    { value: 'warning', label: t('marketing:facebookAds.budget.filters.warning', 'Cảnh báo') },
    { value: 'exhausted', label: t('marketing:facebookAds.budget.filters.exhausted', 'Hết ngân sách') },
  ];

  if (isLoading || authLoading || campaignsLoading) {
    return (
      <div className={className}>
        <Card className="p-6">
          <div className="flex justify-center py-12">
            <div className="text-center">
              <Icon name="loader" className="animate-spin mb-4" size="lg" />
              <Typography variant="body2" className="text-muted-foreground">
                {t('common:loading', 'Đang tải...')}
              </Typography>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Summary Cards */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }} className="mb-6">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:facebookAds.budget.totalBudget', 'Tổng ngân sách')}
              </Typography>
              <Typography variant="h4" className="font-bold text-blue-600">
                {formatCurrency(summaryStats.totalBudget)}
              </Typography>
            </div>
            <Icon name="wallet" size="lg" className="text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:facebookAds.budget.totalSpent', 'Đã chi tiêu')}
              </Typography>
              <Typography variant="h4" className="font-bold text-orange-600">
                {formatCurrency(summaryStats.totalSpent)}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {summaryStats.spentPercentage.toFixed(1)}% {t('marketing:facebookAds.budget.ofTotal', 'của tổng')}
              </Typography>
            </div>
            <Icon name="trending-up" size="lg" className="text-orange-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:facebookAds.budget.remaining', 'Còn lại')}
              </Typography>
              <Typography variant="h4" className="font-bold text-green-600">
                {formatCurrency(summaryStats.totalRemaining)}
              </Typography>
            </div>
            <Icon name="piggy-bank" size="lg" className="text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:facebookAds.budget.alerts', 'Cảnh báo')}
              </Typography>
              <Typography variant="h4" className="font-bold text-red-600">
                {summaryStats.warningCount + summaryStats.exhaustedCount}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {summaryStats.exhaustedCount} {t('marketing:facebookAds.budget.exhausted', 'hết ngân sách')}
              </Typography>
            </div>
            <Icon name="alert-triangle" size="lg" className="text-red-600" />
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Budget Items */}
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-4 sm:space-y-0">
          <Typography variant="h6" className="font-bold">
            {t('marketing:facebookAds.budget.budgetItems', 'Quản lý ngân sách')}
          </Typography>
          
          <Select
            value={budgetFilter}
            onChange={(value) => setBudgetFilter(value as typeof budgetFilter)}
            options={filterOptions}
            className="min-w-[150px]"
          />
        </div>

        <div className="space-y-4">
          {filteredBudgetItems.map((item) => {
            const statusConfig = getStatusConfig(item.status);
            const spentPercentage = (item.spentBudget / item.totalBudget) * 100;
            
            return (
              <Card key={item.id} variant="bordered" className="p-4">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Icon 
                        name={item.type === 'account' ? 'facebook' : 'campaign'} 
                        className={item.type === 'account' ? 'text-blue-600' : 'text-orange-600'} 
                      />
                      <div>
                        <Typography variant="body1" className="font-medium">
                          {item.name}
                        </Typography>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {item.type === 'account' ? t('marketing:facebookAds.budget.account', 'Tài khoản') : t('marketing:facebookAds.budget.campaign', 'Chiến dịch')}
                          </Badge>
                          <Badge variant={statusConfig.variant}>
                            {statusConfig.label}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{t('marketing:facebookAds.budget.spent', 'Đã chi')}: {formatCurrency(item.spentBudget)}</span>
                        <span>{t('marketing:facebookAds.budget.total', 'Tổng')}: {formatCurrency(item.totalBudget)}</span>
                      </div>
                      <Progress value={spentPercentage} className="h-2" />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{spentPercentage.toFixed(1)}% {t('marketing:facebookAds.budget.used', 'đã sử dụng')}</span>
                        {item.estimatedDaysLeft && (
                          <span>
                            ~{item.estimatedDaysLeft} {t('marketing:facebookAds.budget.daysLeft', 'ngày còn lại')}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditBudget(item)}
                    >
                      <Icon name="edit" size="sm" className="mr-1" />
                      {t('common:action.edit', 'Chỉnh sửa')}
                    </Button>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </Card>

      {/* Budget Edit Modal */}
      <Modal
        isOpen={showBudgetModal}
        onClose={() => setShowBudgetModal(false)}
        title={t('marketing:facebookAds.budget.editBudget', 'Chỉnh sửa ngân sách')}
      >
        <Form ref={formRef} onSubmit={handleSubmit}>
          <FormItem label={t('marketing:facebookAds.budget.budgetType', 'Loại ngân sách')} name="budgetType" required>
            <Select
              value={formData.budgetType}
              onChange={(value) => setFormData({ ...formData, budgetType: value as 'daily' | 'lifetime' })}
              options={[
                { value: 'daily', label: t('marketing:facebookAds.budget.daily', 'Hàng ngày') },
                { value: 'lifetime', label: t('marketing:facebookAds.budget.lifetime', 'Trọn đời') },
              ]}
            />
          </FormItem>

          <FormItem label={t('marketing:facebookAds.budget.amount', 'Số tiền')} name="amount" required>
            <Input
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: Number(e.target.value) })}
              placeholder={t('marketing:facebookAds.budget.enterAmount', 'Nhập số tiền')}
            />
          </FormItem>

          <FormItem label={t('marketing:facebookAds.budget.startDate', 'Ngày bắt đầu')} name="startDate" required>
            <Input
              type="date"
              value={formData.startDate}
              onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
            />
          </FormItem>

          {formData.budgetType === 'lifetime' && (
            <FormItem label={t('marketing:facebookAds.budget.endDate', 'Ngày kết thúc')} name="endDate" required>
              <Input
                type="date"
                value={formData.endDate || ''}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
              />
            </FormItem>
          )}

          <div className="flex justify-end space-x-2 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowBudgetModal(false)}
            >
              {t('common:action.cancel', 'Hủy')}
            </Button>
            <Button type="submit" variant="primary">
              {t('common:action.save', 'Lưu')}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default FacebookBudgetManager;
