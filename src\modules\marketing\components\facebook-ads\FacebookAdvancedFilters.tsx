import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Select,
  Input,
  DatePicker,
  Badge,
  Collapsible,
} from '@/shared/components/common';
import { FacebookAdsCampaignStatus, FacebookAdsCampaignObjective } from '../../types/facebook-ads.types';

interface FilterValues {
  search?: string;
  status?: FacebookAdsCampaignStatus[];
  objective?: FacebookAdsCampaignObjective[];
  budgetMin?: number;
  budgetMax?: number;
  spendMin?: number;
  spendMax?: number;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  performance?: {
    ctrMin?: number;
    ctrMax?: number;
    cpcMin?: number;
    cpcMax?: number;
  };
  accountIds?: string[];
}

interface FacebookAdvancedFiltersProps {
  /**
   * Giá trị filter hiện tại
   */
  filters: FilterValues;
  
  /**
   * Callback khi filter thay đổi
   */
  onFiltersChange: (filters: FilterValues) => void;
  
  /**
   * <PERSON><PERSON> sách tài khoản để filter
   */
  accounts?: Array<{ id: string; name: string }>;
  
  /**
   * <PERSON><PERSON><PERSON> thị collapsed mặc định
   */
  defaultCollapsed?: boolean;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Facebook Advanced Filters Component
 * Bộ lọc nâng cao cho Facebook Ads
 */
const FacebookAdvancedFilters: React.FC<FacebookAdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  accounts = [],
  defaultCollapsed = true,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  // Status options
  const statusOptions = [
    { value: 'ACTIVE', label: t('marketing:facebookAds.status.active', 'Hoạt động') },
    { value: 'PAUSED', label: t('marketing:facebookAds.status.paused', 'Tạm dừng') },
    { value: 'DELETED', label: t('marketing:facebookAds.status.deleted', 'Đã xóa') },
    { value: 'ARCHIVED', label: t('marketing:facebookAds.status.archived', 'Lưu trữ') },
  ];

  // Objective options
  const objectiveOptions = [
    { value: 'BRAND_AWARENESS', label: t('marketing:facebookAds.objective.brandAwareness', 'Nhận diện thương hiệu') },
    { value: 'REACH', label: t('marketing:facebookAds.objective.reach', 'Tiếp cận') },
    { value: 'TRAFFIC', label: t('marketing:facebookAds.objective.traffic', 'Lưu lượng truy cập') },
    { value: 'APP_INSTALLS', label: t('marketing:facebookAds.objective.appInstalls', 'Cài đặt ứng dụng') },
    { value: 'VIDEO_VIEWS', label: t('marketing:facebookAds.objective.videoViews', 'Lượt xem video') },
    { value: 'LEAD_GENERATION', label: t('marketing:facebookAds.objective.leadGeneration', 'Tạo khách hàng tiềm năng') },
    { value: 'MESSAGES', label: t('marketing:facebookAds.objective.messages', 'Tin nhắn') },
    { value: 'CONVERSIONS', label: t('marketing:facebookAds.objective.conversions', 'Chuyển đổi') },
    { value: 'CATALOG_SALES', label: t('marketing:facebookAds.objective.catalogSales', 'Bán hàng catalog') },
    { value: 'STORE_TRAFFIC', label: t('marketing:facebookAds.objective.storeTraffic', 'Lưu lượng cửa hàng') },
  ];

  // Account options
  const accountOptions = accounts.map(account => ({
    value: account.id,
    label: account.name,
  }));

  // Update filter value
  const updateFilter = (key: keyof FilterValues, value: unknown) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  // Clear all filters
  const clearAllFilters = () => {
    onFiltersChange({});
  };

  // Count active filters
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.status?.length) count++;
    if (filters.objective?.length) count++;
    if (filters.budgetMin || filters.budgetMax) count++;
    if (filters.spendMin || filters.spendMax) count++;
    if (filters.dateRange) count++;
    if (filters.performance?.ctrMin || filters.performance?.ctrMax) count++;
    if (filters.performance?.cpcMin || filters.performance?.cpcMax) count++;
    if (filters.accountIds?.length) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card className={className}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Icon name="filter" className="text-primary" />
            <Typography variant="h6" className="font-medium">
              {t('marketing:facebookAds.filters.title', 'Bộ lọc nâng cao')}
            </Typography>
            {activeFiltersCount > 0 && (
              <Badge variant="primary">
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
              >
                <Icon name="x" size="sm" className="mr-1" />
                {t('common:action.clearAll', 'Xóa tất cả')}
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              <Icon name={isCollapsed ? 'chevron-down' : 'chevron-up'} size="sm" />
            </Button>
          </div>
        </div>

        {/* Filters Content */}
        <Collapsible isOpen={!isCollapsed}>
          <div className="space-y-4">
            {/* Search */}
            <div>
              <Typography variant="body2" className="font-medium mb-2">
                {t('marketing:facebookAds.filters.search', 'Tìm kiếm')}
              </Typography>
              <Input
                placeholder={t('marketing:facebookAds.filters.searchPlaceholder', 'Tìm theo tên chiến dịch...')}
                value={filters.search || ''}
                onChange={(e) => updateFilter('search', e.target.value)}
                leftIcon={<Icon name="search" size="sm" />}
              />
            </div>

            {/* Status & Objective */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="font-medium mb-2">
                  {t('marketing:facebookAds.filters.status', 'Trạng thái')}
                </Typography>
                <Select
                  multiple
                  value={filters.status || []}
                  onChange={(value) => updateFilter('status', value)}
                  options={statusOptions}
                  placeholder={t('marketing:facebookAds.filters.selectStatus', 'Chọn trạng thái')}
                />
              </div>
              
              <div>
                <Typography variant="body2" className="font-medium mb-2">
                  {t('marketing:facebookAds.filters.objective', 'Mục tiêu')}
                </Typography>
                <Select
                  multiple
                  value={filters.objective || []}
                  onChange={(value) => updateFilter('objective', value)}
                  options={objectiveOptions}
                  placeholder={t('marketing:facebookAds.filters.selectObjective', 'Chọn mục tiêu')}
                />
              </div>
            </div>

            {/* Budget Range */}
            <div>
              <Typography variant="body2" className="font-medium mb-2">
                {t('marketing:facebookAds.filters.budgetRange', 'Khoảng ngân sách (VND)')}
              </Typography>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  type="number"
                  placeholder={t('marketing:facebookAds.filters.minBudget', 'Tối thiểu')}
                  value={filters.budgetMin || ''}
                  onChange={(e) => updateFilter('budgetMin', Number(e.target.value) || undefined)}
                />
                <Input
                  type="number"
                  placeholder={t('marketing:facebookAds.filters.maxBudget', 'Tối đa')}
                  value={filters.budgetMax || ''}
                  onChange={(e) => updateFilter('budgetMax', Number(e.target.value) || undefined)}
                />
              </div>
            </div>

            {/* Spend Range */}
            <div>
              <Typography variant="body2" className="font-medium mb-2">
                {t('marketing:facebookAds.filters.spendRange', 'Khoảng chi tiêu (VND)')}
              </Typography>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  type="number"
                  placeholder={t('marketing:facebookAds.filters.minSpend', 'Tối thiểu')}
                  value={filters.spendMin || ''}
                  onChange={(e) => updateFilter('spendMin', Number(e.target.value) || undefined)}
                />
                <Input
                  type="number"
                  placeholder={t('marketing:facebookAds.filters.maxSpend', 'Tối đa')}
                  value={filters.spendMax || ''}
                  onChange={(e) => updateFilter('spendMax', Number(e.target.value) || undefined)}
                />
              </div>
            </div>

            {/* Date Range */}
            <div>
              <Typography variant="body2" className="font-medium mb-2">
                {t('marketing:facebookAds.filters.dateRange', 'Khoảng thời gian')}
              </Typography>
              <div className="grid grid-cols-2 gap-2">
                <DatePicker
                  value={filters.dateRange?.startDate || ''}
                  onChange={(value) => updateFilter('dateRange', {
                    ...filters.dateRange,
                    startDate: value,
                  })}
                  placeholder={t('marketing:facebookAds.filters.startDate', 'Ngày bắt đầu')}
                />
                <DatePicker
                  value={filters.dateRange?.endDate || ''}
                  onChange={(value) => updateFilter('dateRange', {
                    ...filters.dateRange,
                    endDate: value,
                  })}
                  placeholder={t('marketing:facebookAds.filters.endDate', 'Ngày kết thúc')}
                />
              </div>
            </div>

            {/* Performance Metrics */}
            <div>
              <Typography variant="body2" className="font-medium mb-2">
                {t('marketing:facebookAds.filters.performance', 'Hiệu suất')}
              </Typography>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Typography variant="caption" className="text-muted-foreground mb-1 block">
                    CTR (%)
                  </Typography>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Min"
                      value={filters.performance?.ctrMin || ''}
                      onChange={(e) => updateFilter('performance', {
                        ...filters.performance,
                        ctrMin: Number(e.target.value) || undefined,
                      })}
                    />
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Max"
                      value={filters.performance?.ctrMax || ''}
                      onChange={(e) => updateFilter('performance', {
                        ...filters.performance,
                        ctrMax: Number(e.target.value) || undefined,
                      })}
                    />
                  </div>
                </div>
                
                <div>
                  <Typography variant="caption" className="text-muted-foreground mb-1 block">
                    CPC (VND)
                  </Typography>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.performance?.cpcMin || ''}
                      onChange={(e) => updateFilter('performance', {
                        ...filters.performance,
                        cpcMin: Number(e.target.value) || undefined,
                      })}
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.performance?.cpcMax || ''}
                      onChange={(e) => updateFilter('performance', {
                        ...filters.performance,
                        cpcMax: Number(e.target.value) || undefined,
                      })}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Accounts */}
            {accounts.length > 0 && (
              <div>
                <Typography variant="body2" className="font-medium mb-2">
                  {t('marketing:facebookAds.filters.accounts', 'Tài khoản')}
                </Typography>
                <Select
                  multiple
                  value={filters.accountIds || []}
                  onChange={(value) => updateFilter('accountIds', value)}
                  options={accountOptions}
                  placeholder={t('marketing:facebookAds.filters.selectAccounts', 'Chọn tài khoản')}
                />
              </div>
            )}
          </div>
        </Collapsible>
      </div>
    </Card>
  );
};

export default FacebookAdvancedFilters;
